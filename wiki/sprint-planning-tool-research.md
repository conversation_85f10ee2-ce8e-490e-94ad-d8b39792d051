# Sprint Planning 辅助工具 - 技术调研报告

## 📋 项目概述

基于 Jira Backlog 页面开发一个 Sprint Planning 辅助工具，通过浏览器扩展的形式为团队提供基础的数据展示和分析功能，帮助提升 Sprint 规划效率。

## 🔍 技术可行性分析

### 现有技术基础

**已验证的技术栈：**
- ✅ **前端框架**: Plasmo 0.90.5 + React 18.2.0 + TypeScript
- ✅ **UI 组件库**: Mantine UI 7.11.1 + Tailwind CSS 3.4.1  
- ✅ **状态管理**: Plasmo Storage API
- ✅ **HTTP 客户端**: Fetch API (支持流式响应)
- ✅ **后端**: FastAPI + Azure OpenAI
- ✅ **目标平台**: https://jira.autodesk.com/*

**现有功能基础：**
- ✅ Jira 页面集成 (content script)
- ✅ 后端 API 代理系统
- ✅ 流式响应处理
- ✅ 通知系统
- ✅ 主题管理

### 数据获取策略

**重要发现：** 现有架构通过后端代理调用外部 API，而非直接从浏览器调用 Jira API。

**推荐的数据获取方式：**
1. **DOM 解析** - 从页面中提取可见的 Sprint 和 Issue 数据
2. **后端代理** - 通过后端服务调用 Jira REST API
3. **混合方式** - DOM 解析用于实时数据，API 用于历史数据

**关键 Jira API 端点：**
```
GET /rest/agile/1.0/board/{boardId}/sprint          # 获取 sprints
GET /rest/agile/1.0/sprint/{sprintId}/issue         # 获取 sprint issues  
GET /rest/agile/1.0/board/{boardId}/backlog         # 获取 backlog issues
```

## 🏗️ 简化架构设计

### MVP 功能范围

**Phase 1: 基础数据展示 (2-3 周)**
- Board ID 自动检测
- 当前 Sprint 基础信息展示
- 团队成员 Story Points 分配统计
- 简单的数据可视化

**Phase 2: 历史数据分析 (2-3 周)**  
- 最近 3-5 个 Sprint 的速度趋势
- 团队容量利用率计算
- 基础的规划建议

### 组件架构

```typescript
SprintPlanningWidget/
├── DataExtractor/              # 数据提取
│   ├── DOMParser              # 页面数据解析
│   ├── BoardDetector          # Board ID 检测
│   └── ApiClient              # 后端 API 调用
├── Analytics/                 # 数据分析
│   ├── BasicMetrics           # 基础指标计算
│   └── SimpleRecommendations  # 简单建议
└── UI/                       # 界面组件
    ├── MetricsCard           # 指标卡片
    ├── SimpleChart           # 基础图表
    └── InfoPanel             # 信息面板
```

### 数据流设计

```
1. 页面加载 → 检测 Board ID
2. DOM 解析 → 提取当前可见数据
3. 后端 API → 获取历史数据 (可选)
4. 数据处理 → 计算基础指标
5. UI 渲染 → 显示结果
```

## 🧮 简化算法设计

### 基础指标计算

```typescript
interface BasicMetrics {
  currentSprintPoints: number;
  teamVelocity: number[];        // 最近几个 Sprint 的速度
  memberAllocation: {            // 成员分配
    [memberId: string]: number;
  };
  utilizationRate: number;       // 容量利用率
}

// 简化的计算函数
function calculateBasicMetrics(
  currentIssues: Issue[],
  historicalSprints: Sprint[]
): BasicMetrics {
  // 1. 当前 Sprint Points
  const currentSprintPoints = currentIssues
    .reduce((sum, issue) => sum + (issue.storyPoints || 0), 0);
  
  // 2. 历史速度 (最近 5 个 Sprint)
  const teamVelocity = historicalSprints
    .slice(-5)
    .map(sprint => calculateSprintVelocity(sprint));
  
  // 3. 成员分配
  const memberAllocation = currentIssues
    .reduce((acc, issue) => {
      const assignee = issue.assignee?.id || 'unassigned';
      acc[assignee] = (acc[assignee] || 0) + (issue.storyPoints || 0);
      return acc;
    }, {});
  
  // 4. 利用率 (基于平均速度)
  const avgVelocity = teamVelocity.reduce((a, b) => a + b, 0) / teamVelocity.length;
  const utilizationRate = currentSprintPoints / avgVelocity;
  
  return {
    currentSprintPoints,
    teamVelocity,
    memberAllocation,
    utilizationRate
  };
}
```

## 🎨 UI 设计方案

### 简化的界面布局

```
┌─────────────────────────────────────────────────────────┐
│  🚀 Sprint Planning Assistant                          │
├─────────────────────────────────────────────────────────┤
│  📊 当前 Sprint 概览                                     │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │ 总 Points   │ │ 平均速度     │ │ 利用率       │        │
│  │ 42 SP      │ │ 38 SP      │ │ 110%       │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
├─────────────────────────────────────────────────────────┤
│  📈 成员分配                                             │
│  • John: 12 SP  • Jane: 8 SP  • Bob: 15 SP            │
│  • Alice: 7 SP  • 未分配: 0 SP                         │
├─────────────────────────────────────────────────────────┤
│  💡 简单建议                                             │
│  • 当前分配超出平均速度 10%，建议调整                     │
│  • John 的分配偏高，考虑重新分配部分任务                  │
└─────────────────────────────────────────────────────────┘
```

### 技术实现

```typescript
// 主组件 - 使用现有的 Mantine 组件
const SprintPlanningWidget: React.FC = () => {
  const [metrics, setMetrics] = useState<BasicMetrics | null>(null);
  
  useEffect(() => {
    // 数据获取和处理
    extractAndAnalyzeData().then(setMetrics);
  }, []);
  
  if (!metrics) return <Loader />;
  
  return (
    <Card shadow="sm" padding="md" className="sprint-planning-widget">
      <Stack spacing="md">
        <MetricsOverview metrics={metrics} />
        <MemberAllocation allocation={metrics.memberAllocation} />
        <SimpleRecommendations metrics={metrics} />
      </Stack>
    </Card>
  );
};

// 使用现有依赖，无需额外图表库
const MetricsOverview: React.FC<{ metrics: BasicMetrics }> = ({ metrics }) => {
  return (
    <SimpleGrid cols={3} spacing="sm">
      <Paper p="md" withBorder>
        <Text size="sm" color="dimmed">总 Story Points</Text>
        <Text size="xl" weight={500}>{metrics.currentSprintPoints} SP</Text>
      </Paper>
      <Paper p="md" withBorder>
        <Text size="sm" color="dimmed">平均速度</Text>
        <Text size="xl" weight={500}>
          {Math.round(metrics.teamVelocity.reduce((a, b) => a + b, 0) / metrics.teamVelocity.length)} SP
        </Text>
      </Paper>
      <Paper p="md" withBorder>
        <Text size="sm" color="dimmed">利用率</Text>
        <Text size="xl" weight={500} color={metrics.utilizationRate > 1 ? "red" : "green"}>
          {Math.round(metrics.utilizationRate * 100)}%
        </Text>
      </Paper>
    </SimpleGrid>
  );
};
```

## 🔧 实施计划

### Phase 1: MVP 开发 (2-3 周)

**Week 1:**
- 设置开发环境和项目结构
- 实现 Board ID 检测功能
- 开发基础的 DOM 数据解析

**Week 2:**
- 创建基础 UI 组件
- 实现简单的指标计算
- 集成到 Jira 页面

**Week 3:**
- 测试和优化
- 用户反馈收集
- 文档编写

### Phase 2: 功能增强 (2-3 周)

**Week 4-5:**
- 添加历史数据获取
- 实现速度趋势分析
- 改进 UI 和用户体验

**Week 6:**
- 性能优化
- 错误处理完善
- 部署和发布

## ⚠️ 风险评估

### 技术风险

1. **数据获取限制**
   - 风险：DOM 结构变化可能影响数据解析
   - 缓解：提供多种数据获取方式，增加容错机制

2. **性能影响**
   - 风险：数据处理可能影响页面性能
   - 缓解：异步处理，避免阻塞主线程

3. **权限限制**
   - 风险：可能无法访问所有需要的 Jira 数据
   - 缓解：从可访问的数据开始，逐步扩展

### 业务风险

1. **用户接受度**
   - 风险：用户可能不需要这些功能
   - 缓解：从简单功能开始，根据反馈迭代

2. **维护成本**
   - 风险：Jira 更新可能需要频繁维护
   - 缓解：设计灵活的架构，减少对特定 DOM 结构的依赖

## 📝 结论

**技术可行性：** ✅ 高
- 基于现有技术栈，风险较低
- 从简单功能开始，可逐步扩展

**推荐方案：**
1. 先开发 MVP 版本，验证用户需求
2. 使用 DOM 解析 + 后端代理的混合方式获取数据
3. 重点关注基础指标展示，避免过度设计
4. 保持简单的架构，便于维护和扩展

**下一步行动：**
1. 创建项目分支和开发环境
2. 实现 Board ID 检测和基础数据解析
3. 开发简单的 UI 原型
4. 收集用户反馈，验证需求假设
