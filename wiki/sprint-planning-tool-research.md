# Sprint Planning 辅助工具 - 技术调研报告

## 📋 项目概述

基于 Jira Backlog 页面开发一个 Sprint Planning 辅助工具，通过浏览器扩展的形式为团队提供基础的数据展示和分析功能，帮助提升 Sprint 规划效率。

## 🔍 技术可行性分析

### 现有技术基础

**已验证的技术栈：**
- ✅ **前端框架**: Plasmo 0.90.5 + React 18.2.0 + TypeScript
- ✅ **UI 组件库**: Mantine UI 7.11.1 + Tailwind CSS 3.4.1  
- ✅ **状态管理**: Plasmo Storage API
- ✅ **HTTP 客户端**: Fetch API (支持流式响应)
- ✅ **后端**: FastAPI + Azure OpenAI
- ✅ **目标平台**: https://jira.autodesk.com/*

**现有功能基础：**
- ✅ Jira 页面集成 (content script)
- ✅ 后端 API 代理系统
- ✅ 流式响应处理
- ✅ 通知系统
- ✅ 主题管理

### 数据获取策略

**重要发现：** 现有架构通过后端代理调用外部 API，而非直接从浏览器调用 Jira API。

**推荐的数据获取方式：**
1. **DOM 解析** - 从页面中提取可见的 Sprint 和 Issue 数据
2. **后端代理** - 通过后端服务调用 Jira REST API
3. **混合方式** - DOM 解析用于实时数据，API 用于历史数据

**关键 Jira API 端点：**
```
GET /rest/agile/1.0/board/{boardId}/sprint          # 获取 sprints
GET /rest/agile/1.0/sprint/{sprintId}/issue         # 获取 sprint issues  
GET /rest/agile/1.0/board/{boardId}/backlog         # 获取 backlog issues
```

## 🏗️ 简化架构设计

### MVP 功能范围

**Phase 1: 基础数据展示 (2-3 周)**
- Board ID 自动检测
- 当前 Sprint 基础信息展示
- 团队成员 Story Points 分配统计
- 简单的数据可视化

**Phase 2: 历史数据分析 (2-3 周)**  
- 最近 3-5 个 Sprint 的速度趋势
- 团队容量利用率计算
- 基础的规划建议

### 组件架构

```typescript
SprintPlanningWidget/
├── DataExtractor/              # 数据提取
│   ├── DOMParser              # 页面数据解析
│   ├── BoardDetector          # Board ID 检测
│   └── ApiClient              # 后端 API 调用
├── Analytics/                 # 数据分析
│   ├── BasicMetrics           # 基础指标计算
│   └── SimpleRecommendations  # 简单建议
└── UI/                       # 界面组件
    ├── MetricsCard           # 指标卡片
    ├── SimpleChart           # 基础图表
    └── InfoPanel             # 信息面板
```

### 数据流设计

```
1. 页面加载 → 检测 Board ID
2. DOM 解析 → 提取当前可见数据
3. 后端 API → 获取历史数据 (可选)
4. 数据处理 → 计算基础指标
5. UI 渲染 → 显示结果
```

## 🧮 简化算法设计

### 基础指标计算

```typescript
interface BasicMetrics {
  currentSprintPoints: number;
  teamVelocity: number[];        // 最近几个 Sprint 的速度
  memberAllocation: {            // 成员分配
    [memberId: string]: number;
  };
  utilizationRate: number;       // 容量利用率
}

// 简化的计算函数
function calculateBasicMetrics(
  currentIssues: Issue[],
  historicalSprints: Sprint[]
): BasicMetrics {
  // 1. 当前 Sprint Points
  const currentSprintPoints = currentIssues
    .reduce((sum, issue) => sum + (issue.storyPoints || 0), 0);
  
  // 2. 历史速度 (最近 5 个 Sprint)
  const teamVelocity = historicalSprints
    .slice(-5)
    .map(sprint => calculateSprintVelocity(sprint));
  
  // 3. 成员分配
  const memberAllocation = currentIssues
    .reduce((acc, issue) => {
      const assignee = issue.assignee?.id || 'unassigned';
      acc[assignee] = (acc[assignee] || 0) + (issue.storyPoints || 0);
      return acc;
    }, {});
  
  // 4. 利用率 (基于平均速度)
  const avgVelocity = teamVelocity.reduce((a, b) => a + b, 0) / teamVelocity.length;
  const utilizationRate = currentSprintPoints / avgVelocity;
  
  return {
    currentSprintPoints,
    teamVelocity,
    memberAllocation,
    utilizationRate
  };
}
```

## 🎨 UI 设计方案

### 增强的界面布局

```
┌─────────────────────────────────────────────────────────┐
│  🚀 Sprint Planning Assistant                          │
├─────────────────────────────────────────────────────────┤
│  📊 当前 Sprint 概览                                     │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │ 总 Points   │ │ 平均速度     │ │ 利用率       │        │
│  │ 42 SP      │ │ 38 SP      │ │ 110%       │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
├─────────────────────────────────────────────────────────┤
│  � 团队工作负载详情                                     │
│  ┌─────────────────────────────────────────────────────┐ │
│  │ 成员      │当前Sprint│上周遗留│总负载│状态    │       │ │
│  ├─────────────────────────────────────────────────────┤ │
│  │ John      │   8 SP  │  4 SP │ 12 SP│ ⚠️ 偏高 │       │ │
│  │ Jane      │   6 SP  │  2 SP │  8 SP│ ✅ 正常 │       │ │
│  │ Bob       │  10 SP  │  5 SP │ 15 SP│ 🔴 过载 │       │ │
│  │ Alice     │   5 SP  │  2 SP │  7 SP│ ✅ 正常 │       │ │
│  │ 未分配     │   0 SP  │  0 SP │  0 SP│ -      │       │ │
│  └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│  🏖️ Next Sprint 假期影响                                │
│  ┌─────────────────────────────────────────────────────┐ │
│  │ 地区/国家  │ 假期名称     │ 日期范围    │ 影响成员 │   │ │
│  ├─────────────────────────────────────────────────────┤ │
│  │ 🇨🇳 中国    │ 春节假期     │ 2/10-2/17  │ 2人     │   │ │
│  │ 🇺🇸 美国    │ 总统日       │ 2/19       │ 1人     │   │ │
│  │ 📊 容量影响 │ 预计减少25%  │ 建议调整至 │ 28-32SP │   │ │
│  └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│  💡 智能建议 (AI 辅助)                                   │
│  • 🤖 基于历史数据：Bob 连续3个Sprint超载，建议减少分配    │
│  • 📅 考虑假期影响：下个Sprint建议总量控制在30SP以内      │
│  • ⚖️ 负载均衡：建议将Bob的2个SP转移给Alice              │
│  • 📈 趋势预警：团队速度呈下降趋势，关注团队状态          │
└─────────────────────────────────────────────────────────┘
```

### Sprint 规划流程可视化

```mermaid
flowchart TD
    A[开始 Sprint Planning] --> B[检测当前 Board]
    B --> C[获取团队数据]
    C --> D[分析历史速度]
    D --> E[计算成员负载]
    E --> F[检查假期影响]
    F --> G[生成 AI 建议]
    G --> H[展示规划仪表板]
    H --> I{需要调整?}
    I -->|是| J[重新分配任务]
    I -->|否| K[确认 Sprint 计划]
    J --> E
    K --> L[开始 Sprint]

    style A fill:#e1f5fe
    style K fill:#c8e6c9
    style L fill:#c8e6c9
    style G fill:#fff3e0
```

### 数据流架构图

```mermaid
graph LR
    A[Jira 页面 DOM] --> B[数据提取器]
    C[后端 API] --> B
    B --> D[数据处理引擎]
    D --> E[指标计算]
    D --> F[假期分析]
    D --> G[AI 建议生成]
    E --> H[UI 渲染层]
    F --> H
    G --> H
    H --> I[用户界面]

    subgraph "数据源"
        A
        C
    end

    subgraph "处理层"
        B
        D
        E
        F
        G
    end

    subgraph "展示层"
        H
        I
    end

    style A fill:#ffebee
    style C fill:#ffebee
    style G fill:#fff3e0
    style I fill:#e8f5e8
```

### 核心组件设计

#### 1. 增强的团队工作负载组件

```typescript
interface MemberWorkload {
  id: string;
  name: string;
  currentSprintPoints: number;
  carryOverPoints: number;      // 上周遗留
  totalWorkload: number;
  status: 'normal' | 'high' | 'overloaded';
  avatar?: string;
}

const TeamWorkloadTable: React.FC<{ workloads: MemberWorkload[] }> = ({ workloads }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'normal': return 'green';
      case 'high': return 'yellow';
      case 'overloaded': return 'red';
      default: return 'gray';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'normal': return '✅';
      case 'high': return '⚠️';
      case 'overloaded': return '🔴';
      default: return '-';
    }
  };

  return (
    <Table striped highlightOnHover>
      <thead>
        <tr>
          <th>成员</th>
          <th>当前Sprint</th>
          <th>上周遗留</th>
          <th>总负载</th>
          <th>状态</th>
        </tr>
      </thead>
      <tbody>
        {workloads.map((member) => (
          <tr key={member.id}>
            <td>
              <Group spacing="sm">
                {member.avatar && <Avatar src={member.avatar} size="sm" />}
                <Text weight={500}>{member.name}</Text>
              </Group>
            </td>
            <td>{member.currentSprintPoints} SP</td>
            <td>
              <Text color={member.carryOverPoints > 0 ? 'orange' : 'dimmed'}>
                {member.carryOverPoints} SP
              </Text>
            </td>
            <td><Text weight={500}>{member.totalWorkload} SP</Text></td>
            <td>
              <Badge
                color={getStatusColor(member.status)}
                variant="light"
                leftSection={getStatusIcon(member.status)}
              >
                {member.status === 'normal' ? '正常' :
                 member.status === 'high' ? '偏高' : '过载'}
              </Badge>
            </td>
          </tr>
        ))}
      </tbody>
    </Table>
  );
};
```

#### 2. 假期影响分析组件

```typescript
interface HolidayImpact {
  region: string;
  country: string;
  holidayName: string;
  dateRange: string;
  affectedMembers: number;
  capacityReduction: number; // 百分比
}

const HolidayImpactPanel: React.FC<{ holidays: HolidayImpact[], currentCapacity: number }> = ({
  holidays,
  currentCapacity
}) => {
  const totalReduction = holidays.reduce((sum, h) => sum + h.capacityReduction, 0);
  const adjustedCapacity = Math.round(currentCapacity * (1 - totalReduction / 100));

  return (
    <Stack spacing="sm">
      <Group position="apart">
        <Text weight={500} size="lg">🏖️ Next Sprint 假期影响</Text>
        <Badge color="orange" variant="light">
          容量减少 {totalReduction}%
        </Badge>
      </Group>

      <Table size="sm">
        <thead>
          <tr>
            <th>地区/国家</th>
            <th>假期名称</th>
            <th>日期范围</th>
            <th>影响成员</th>
          </tr>
        </thead>
        <tbody>
          {holidays.map((holiday, index) => (
            <tr key={index}>
              <td>{holiday.country} {holiday.region}</td>
              <td>{holiday.holidayName}</td>
              <td>{holiday.dateRange}</td>
              <td>{holiday.affectedMembers}人</td>
            </tr>
          ))}
        </tbody>
      </Table>

      <Alert icon="📊" color="blue" variant="light">
        <Text size="sm">
          <strong>容量调整建议：</strong>
          从 {currentCapacity}SP 调整至 {adjustedCapacity}SP
        </Text>
      </Alert>
    </Stack>
  );
};
```

#### 3. AI 辅助建议组件

```typescript
interface AIRecommendation {
  type: 'historical' | 'holiday' | 'balance' | 'trend';
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  actionable: boolean;
}

const AIRecommendationsPanel: React.FC<{
  recommendations: AIRecommendation[],
  isAIEnabled: boolean
}> = ({ recommendations, isAIEnabled }) => {
  const getRecommendationIcon = (type: string) => {
    switch (type) {
      case 'historical': return '🤖';
      case 'holiday': return '📅';
      case 'balance': return '⚖️';
      case 'trend': return '📈';
      default: return '💡';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'red';
      case 'medium': return 'yellow';
      case 'low': return 'blue';
      default: return 'gray';
    }
  };

  if (!isAIEnabled) {
    return (
      <Alert color="gray" variant="light">
        <Text size="sm">
          💡 <strong>基础建议模式</strong> - 升级到 AI 辅助模式获得更智能的建议
        </Text>
      </Alert>
    );
  }

  return (
    <Stack spacing="xs">
      <Group position="apart">
        <Text weight={500} size="lg">💡 智能建议 (AI 辅助)</Text>
        <Badge color="blue" variant="light">AI 分析</Badge>
      </Group>

      {recommendations.map((rec, index) => (
        <Alert
          key={index}
          icon={getRecommendationIcon(rec.type)}
          color={getPriorityColor(rec.priority)}
          variant="light"
        >
          <Group position="apart" align="flex-start">
            <div style={{ flex: 1 }}>
              <Text size="sm" weight={500}>{rec.title}</Text>
              <Text size="xs" color="dimmed">{rec.description}</Text>
            </div>
            {rec.actionable && (
              <Button size="xs" variant="light" color={getPriorityColor(rec.priority)}>
                应用建议
              </Button>
            )}
          </Group>
        </Alert>
      ))}
    </Stack>
  );
};
```

## 🔧 实施计划

### Phase 1: 核心功能开发 (2-3 周)

**Week 1: 基础架构**
- 设置开发环境和项目结构
- 实现 Board ID 检测功能
- 开发基础的 DOM 数据解析
- 创建团队工作负载表格组件

**Week 2: 数据分析与展示**
- 实现成员负载计算（当前 + 遗留）
- 开发假期影响分析功能
- 集成基础 UI 组件到 Jira 页面
- 添加简单的建议系统

**Week 3: 优化与测试**
- UI/UX 优化和响应式设计
- 错误处理和边界情况
- 用户反馈收集和迭代

### Phase 2: AI 增强功能 (2-3 周) 【可选】

**Week 4: AI 集成准备**
- 评估现有 Azure OpenAI 后端集成可行性
- 设计 AI 建议的数据输入格式
- 开发 AI 建议组件框架

**Week 5: 智能分析**
- 实现基于历史数据的 AI 分析
- 集成假期影响到 AI 建议系统
- 开发可操作的建议功能

**Week 6: 完善与部署**
- AI 建议准确性调优
- 性能优化和缓存策略
- 生产环境部署

## ⚠️ 风险评估

### 技术风险

1. **数据获取限制**
   - 风险：DOM 结构变化可能影响数据解析
   - 缓解：提供多种数据获取方式，增加容错机制

2. **性能影响**
   - 风险：数据处理可能影响页面性能
   - 缓解：异步处理，避免阻塞主线程

3. **权限限制**
   - 风险：可能无法访问所有需要的 Jira 数据
   - 缓解：从可访问的数据开始，逐步扩展

### 业务风险

1. **用户接受度**
   - 风险：用户可能不需要这些功能
   - 缓解：从简单功能开始，根据反馈迭代

2. **维护成本**
   - 风险：Jira 更新可能需要频繁维护
   - 缓解：设计灵活的架构，减少对特定 DOM 结构的依赖

3. **AI 功能风险**
   - 风险：AI 建议可能不准确或不实用
   - 缓解：提供 AI 开关选项，保持基础功能独立运行
   - 缓解：建立反馈机制，持续改进 AI 模型

4. **假期数据准确性**
   - 风险：假期信息可能过时或不完整
   - 缓解：使用可靠的假期 API 或允许用户自定义
   - 缓解：提供手动调整功能

## 📝 结论

**技术可行性：** ✅ 高
- 基于现有技术栈，风险较低
- 从简单功能开始，可逐步扩展

**推荐方案：**
1. **MVP 优先**：先开发核心的工作负载展示功能
2. **数据策略**：DOM 解析 + 后端代理的混合方式
3. **渐进增强**：基础功能稳定后再考虑 AI 辅助
4. **用户体验**：重点关注清晰的数据展示和实用的建议

**功能优先级：**
- 🔥 **高优先级**：团队工作负载表格、基础指标展示
- 🔶 **中优先级**：假期影响分析、历史趋势
- 🔵 **低优先级**：AI 辅助建议、高级可视化

**下一步行动：**
1. 创建项目分支和开发环境
2. 实现增强的团队工作负载表格
3. 开发假期影响分析功能
4. 评估 AI 集成的技术可行性
5. 收集用户反馈，验证功能价值
